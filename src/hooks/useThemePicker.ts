
'use client'

import { useState, useEffect, useCallback, useMemo } from 'react';
import { hex2oklch } from 'colorizr';

import type { ThemeColors, ThemeConfig, ColorRelations, ThemeChangeCallback } from '@/types';

/**
 * React hook for managing dynamic theme colors
 *
 * @param config - Configuration options for the theme system
 * @returns Object with theme colors and functions to update the theme
 */
export function useThemePicker(config: ThemeConfig = {}) {
  const {
    initialColor = '#000000',
    enableTransitions = true,
    colorRelations = {},
    storageKey = 'theme-color'
  } = config;

  const defaultRelations: Required<ColorRelations> = useMemo(() => ({
    surfaceLightness: 1.2,
    brandLightness: 3,
    brandChroma: 5,
    borderLightness: 1.4,
    borderChroma: 0.5,
    ...colorRelations
  }), [colorRelations]);

  /**
   * Get stored theme color from localStorage
   */
  const getStoredColor = useCallback((): string => {
    if (typeof window === 'undefined') return initialColor;

    try {
      const stored = localStorage.getItem(storageKey);
      return stored || initialColor;
    } catch (error) {
      console.warn('Failed to read from localStorage:', error);
      return initialColor;
    }
  }, [initialColor, storageKey]);

  /**
   * Save theme color to localStorage
   */
  const saveColorToStorage = useCallback((color: string): void => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(storageKey, color);
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  }, [storageKey]);

  const [colors, setColors] = useState<ThemeColors>(() => {
    const storedColor = getStoredColor();
    return {
      baseColor: storedColor,
      lightness: 60,
      isDarkText: false
    };
  });

  /**
   * Convert hex color to lightness value (0-100)
   * Uses weighted RGB formula for perceived brightness
   */
  const getLightnessFromHex = useCallback((hex: string): number => {
    // Remove hashtag if present
    const cleanHex = hex.replace('#', '');

    // Extract RGB values
    const r = parseInt(cleanHex.substr(0, 2), 16) / 255;
    const g = parseInt(cleanHex.substr(2, 2), 16) / 255;
    const b = parseInt(cleanHex.substr(4, 2), 16) / 255;

    // Calculate perceived brightness using weighted formula
    // Human eyes are more sensitive to green than red or blue
    const brightness = (0.299 * r + 0.587 * g + 0.114 * b) * 100;

    return brightness;
  }, []);

  /**
   * Update the theme with a new base color
   */
  const updateTheme = useCallback((color: string, callback?: ThemeChangeCallback) => {
    const lightness = getLightnessFromHex(color);
    const isDarkText = lightness > 60;

    const oklch = hex2oklch(color);

    // Update CSS custom properties using both Tailwind v4 and legacy variable names
    const root = document.documentElement;

    // Base color (both new and legacy names)
    root.style.setProperty('--color-base', `oklch(${oklch.l * 100}% ${oklch.c} ${oklch.h})`);
    root.style.setProperty('--base-color', `oklch(${oklch.l * 100}% ${oklch.c} ${oklch.h})`); // Legacy

    // Surface color (brighter)
    root.style.setProperty(
      '--color-surface',
      `oklch(from var(--color-base) calc(l * ${defaultRelations.surfaceLightness}) c h)`
    );
    root.style.setProperty('--surface-color', 'var(--color-surface)'); // Legacy alias

    // Brand color (more saturated and brighter)
    root.style.setProperty(
      '--color-brand',
      `oklch(from var(--color-base) calc(l * ${defaultRelations.brandLightness}) calc(c * ${defaultRelations.brandChroma}) h)`
    );
    root.style.setProperty('--brand-color', 'var(--color-brand)'); // Legacy alias

    // Border color (subtle variation)
    root.style.setProperty(
      '--color-border',
      `oklch(from var(--color-base) calc(l * ${defaultRelations.borderLightness}) calc(c * ${defaultRelations.borderChroma}) h)`
    );
    root.style.setProperty('--border-color', 'var(--color-border)'); // Legacy alias

    // Text color based on background brightness
    const textColor = isDarkText ? 'black' : 'white';
    root.style.setProperty('--color-text', textColor);
    root.style.setProperty('--text-color', textColor); // Legacy

    // Secondary text color with transparency
    root.style.setProperty(
      '--color-text-secondary',
      `oklch(from var(--color-text) l c h / 0.6)`
    );
    root.style.setProperty('--secondary-text-color', 'var(--color-text-secondary)'); // Legacy alias

    // Enable/disable transitions
    if (enableTransitions) {
      root.style.setProperty('--theme-transition', 'all 0.3s ease');
    }

    const newColors: ThemeColors = { baseColor: color, lightness, isDarkText };
    setColors(newColors);

    // Save to localStorage
    saveColorToStorage(color);

    // Call optional callback
    callback?.(newColors);
  }, [getLightnessFromHex, defaultRelations, enableTransitions, saveColorToStorage]);

  /**
   * Apply a random theme from predefined colors
   */
  const applyRandomTheme = useCallback((callback?: ThemeChangeCallback) => {
    const randomColors = [
      '#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24',
      '#f0932b', '#eb4d4b', '#6ab04c', '#be2edd',
      '#0077be', '#228B22', '#FF6347', '#663399'
    ];

    const randomColor = randomColors[Math.floor(Math.random() * randomColors.length)];
    updateTheme(randomColor, callback);
  }, [updateTheme]);

  /**
   * Reset to initial theme
   */
  const resetTheme = useCallback((callback?: ThemeChangeCallback) => {
    updateTheme(initialColor, callback);
  }, [initialColor, updateTheme]);

  /**
   * Clear stored theme color from localStorage
   */
  const clearStoredColor = useCallback((): void => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem(storageKey);
    } catch (error) {
      console.warn('Failed to clear from localStorage:', error);
    }
  }, [storageKey]);

  // Initialize theme on mount (runs only once)
  useEffect(() => {
    const storedColor = getStoredColor();
    updateTheme(storedColor);
  }, []); // Empty dependency array - runs only on mount

  return {
    colors,
    updateTheme,
    applyRandomTheme,
    resetTheme,
    clearStoredColor,
    getLightnessFromHex
  };
}

export default useThemePicker;
