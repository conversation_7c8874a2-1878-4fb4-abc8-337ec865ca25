// React Router
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";

// Types
import type { RootLayoutProps } from "@/types/app";

// Redux
import { useAppSelector } from "@/hooks/redux";

// Layout Components
import Header from "@/components/layout/Header";
import Sidebar from "@/components/layout/Sidebar";
import Footer from "@/components/layout/Footer";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";

// Utils
import { cn } from "@/utils";

import Home from "@/pages/Home";
import Dashboard from "@/pages/Dashboard";
import About from "@/pages/About";
import Profile from "@/pages/Profile";
import Settings from "@/pages/Settings";

const AppContent = () => {
  const { sidebarOpen } = useAppSelector((state: RootLayoutProps) => state.app);

  return (
    <Router>
      <div className="min-h-screen bg-secondary-50 flex">
        {/* Sidebar */}
        <Sidebar />

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <Header />

          {/* Main Content Area */}
          <main
            className={cn(
              "flex-1 p-6 transition-all duration-300",
              sidebarOpen ? "md:ml-0" : "md:ml-0"
            )}
          >
            <ProtectedRoute>
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/about" element={<About />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            </ProtectedRoute>
          </main>

          {/* Footer */}
          <Footer />
        </div>
      </div>
    </Router>
  );
};

export default AppContent;
