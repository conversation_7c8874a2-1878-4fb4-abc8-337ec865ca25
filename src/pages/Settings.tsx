// Localization
import { useTranslation } from "react-i18next";

// UI Components
import { <PERSON>, Button } from "@/components/ui";
import { Settings as SettingsIcon, Globe, Palette, Bell } from "lucide-react";
import LanguageSelector from "@/components/features/LanguageSelector";
import { ThemePicker } from "@/components/ThemePicker";
import { useThemePicker } from "@/hooks/useThemePicker";

//types
import type { ThemeColors, ColorPreset } from "@/types";

const Settings = () => {
  const { t } = useTranslation("common");

  const colorPresets: ColorPreset[] = [
    { name: "Blue", color: "#3b82f6", description: "Professional blue" },
    { name: "Green", color: "#10b981", description: "Nature green" },
    { name: "Purple", color: "#8b5cf6", description: "Creative purple" },
    { name: "Red", color: "#ef4444", description: "Bold red" },
    { name: "Orange", color: "#f97316", description: "Energetic orange" },
    { name: "Pink", color: "#ec4899", description: "Vibrant pink" },
  ];

  const { colors, updateTheme } = useThemePicker({
    initialColor: "#3b82f6",
    enableTransitions: true,
    colorRelations: {
      surfaceLightness: 1.2,
      brandChroma: 5,
    },
  });

  const handleThemeChange = (color: string) => {
    updateTheme(color, (newColors: ThemeColors) => {
      console.log("Theme updated:", newColors);
    });
  };

  return (
    <div className="mb-8">
      <Card padding="lg" className="text-center mb-6">
        <h1 className="text-3xl font-bold mb-2 animate-fade-in">
          {t("settings.title")}
        </h1>
        <p className="text-lg max-w-2xl mx-auto animate-fade-in">
          {t("settings.description")}
        </p>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card padding="lg">
          <div className="flex items-center mb-4">
            <Globe className="w-6 h-6 text-primary-600 mr-3" />
            <h2 className="text-xl font-bold">{t("settings.language")}</h2>
          </div>
          <p className="text-secondary-600 mb-4">
            {t("settings.languageDescription")}
          </p>
          <LanguageSelector />
        </Card>

        <Card padding="lg">
          <div className="flex items-center mb-4">
            <Palette className="w-6 h-6 text-primary-600 mr-3" />
            <h2 className="text-xl font-bold">{t("settings.theme")}</h2>
          </div>
          <p className="text-secondary-600 mb-4">
            {t("settings.themeDescription")}
          </p>

          <ThemePicker
            colors={colors}
            onColorChange={handleThemeChange}
            label={t("settings.theme")}
            className="settings-theme-picker"
            showPresets={true}
            presets={colorPresets}
            compact={false}
          />
        </Card>

        <Card padding="lg">
          <div className="flex items-center mb-4">
            <Bell className="w-6 h-6 text-primary-600 mr-3" />
            <h2 className="text-xl font-bold">{t("settings.notifications")}</h2>
          </div>
          <p className="text-secondary-600 mb-4">
            {t("settings.notificationsDescription")}
          </p>
          <div className="text-secondary-500">{t("settings.comingSoon")}</div>
        </Card>

        <Card padding="lg">
          <div className="flex items-center mb-4">
            <SettingsIcon className="w-6 h-6 text-primary-600 mr-3" />
            <h2 className="text-xl font-bold">{t("settings.general")}</h2>
          </div>
          <p className="text-secondary-600 mb-4">
            {t("settings.generalDescription")}
          </p>
          <div className="text-secondary-500">{t("settings.comingSoon")}</div>
        </Card>
      </div>
    </div>
  );
};

export default Settings;
