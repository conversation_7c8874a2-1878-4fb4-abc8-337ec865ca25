// Localization
import { useTranslation } from "react-i18next";

// UI Components
import { Card } from "@/components/ui";
import { Star, Heart, Users } from "lucide-react";

const About = () => {
  const { t } = useTranslation("common");

  return (
    <div className="mb-8">
      <Card padding="lg" className="text-center mb-6">
        <h1 className="text-3xl font-bold mb-2 animate-fade-in">
          {t("about.title")}
        </h1>
        <p className="text-lg max-w-2xl mx-auto animate-fade-in">
          {t("about.description")}
        </p>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card padding="lg" className="text-center">
          <Star className="w-12 h-12 text-primary-600 mb-4 mx-auto" />
          <h2 className="text-xl font-bold mb-2">{t("about.mission")}</h2>
          <p className="text-secondary-600">
            {t("about.missionDescription")}
          </p>
        </Card>
        
        <Card padding="lg" className="text-center">
          <Heart className="w-12 h-12 text-primary-600 mb-4 mx-auto" />
          <h2 className="text-xl font-bold mb-2">{t("about.vision")}</h2>
          <p className="text-secondary-600">
            {t("about.visionDescription")}
          </p>
        </Card>
        
        <Card padding="lg" className="text-center">
          <Users className="w-12 h-12 text-primary-600 mb-4 mx-auto" />
          <h2 className="text-xl font-bold mb-2">{t("about.team")}</h2>
          <p className="text-secondary-600">
            {t("about.teamDescription")}
          </p>
        </Card>
      </div>
    </div>
  );
};

export default About;
