// Localization
import { useTranslation } from "react-i18next";

// UI Components
import { Card } from "@/components/ui";

const Home = () => {
  const { t } = useTranslation("common");

  return (
    <div className="mb-8">
      <Card padding="lg" className="text-center">
        <h1 className="text-3xl font-bold mb-2 animate-fade-in">
          {t("home.title")}
        </h1>
        <p className="text-lg max-w-2xl mx-auto animate-fade-in">
          {t("home.description")}
        </p>
      </Card>
    </div>
  );
};

export default Home;
