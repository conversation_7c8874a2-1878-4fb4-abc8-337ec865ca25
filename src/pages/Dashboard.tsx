// Localization
import { useTranslation } from "react-i18next";

// UI Components
import { Bar<PERSON>hart, TrendingUp, Users, Activity } from "lucide-react";
import { Card } from "@/components/ui";

const Dashboard = () => {
  const { t } = useTranslation("common");

  return (
    <div className="mb-8">
      <Card padding="lg" className="text-center">
        <h1 className="text-3xl font-bold mb-2 animate-fade-in">
          {t("dashboard.title")}
        </h1>
        <p className="text-lg max-w-2xl mx-auto animate-fade-in">
          {t("dashboard.description")}
        </p>
      </Card>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card padding="lg">
          <BarChart className="w-12 h-12 text-primary-600 mb-4" />
          <h2 className="text-xl font-bold mb-2">{t("dashboard.revenue")}</h2>
          <p className="text-secondary-600">
            {t("dashboard.revenueDescription")}
          </p>
        </Card>
        <Card padding="lg">
          <TrendingUp className="w-12 h-12 text-primary-600 mb-4" />
          <h2 className="text-xl font-bold mb-2">
            {t("dashboard.performance")}
          </h2>
          <p className="text-secondary-600">
            {t("dashboard.performanceDescription")}
          </p>
        </Card>
        <Card padding="lg">
          <Users className="w-12 h-12 text-primary-600 mb-4" />
          <h2 className="text-xl font-bold mb-2">{t("dashboard.users")}</h2>
          <p className="text-secondary-600">
            {t("dashboard.usersDescription")}
          </p>
        </Card>
        <Card padding="lg">
          <Activity className="w-12 h-12 text-primary-600 mb-4" />
          <h2 className="text-xl font-bold mb-2">{t("dashboard.activity")}</h2>
          <p className="text-secondary-600">
            {t("dashboard.activityDescription")}
          </p>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
