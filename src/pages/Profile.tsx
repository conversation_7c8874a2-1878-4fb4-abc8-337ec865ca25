// Localization
import { useTranslation } from "react-i18next";

// UI Components
import { Card } from "@/components/ui";
import UserProfile from "@/components/features/UserProfile";

const Profile = () => {
  const { t } = useTranslation("common");

  return (
    <div className="mb-8">
      <Card padding="lg" className="text-center mb-6">
        <h1 className="text-3xl font-bold mb-2 animate-fade-in">
          {t("profile.title")}
        </h1>
        <p className="text-lg max-w-2xl mx-auto animate-fade-in">
          {t("profile.description")}
        </p>
      </Card>

      <div className="max-w-2xl mx-auto">
        <UserProfile />
      </div>
    </div>
  );
};

export default Profile;
