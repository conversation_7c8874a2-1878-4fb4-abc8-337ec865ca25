{"app": {"title": "Nakshatra Cafe", "description": "Nakshatra Cafe is a Vedic astrology application", "welcome": "Welcome to Nakshatra Cafe App!", "loading": "Loading...", "error": "Something went wrong", "tryAgain": "Try again", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh"}, "navigation": {"menu": "<PERSON><PERSON>", "home": "Home", "profile": "Profile", "settings": "Settings", "about": "About", "contact": "Contact", "dashboard": "Dashboard", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register"}, "auth": {"loginTitle": "Welcome Back", "loginSubtitle": "Sign in to your account to continue", "email": "Email", "password": "Password", "loginButton": "Sign In", "logout": "Logout"}, "home": {"title": "Welcome to Nakshatra Cafe", "description": "Discover the wisdom of Vedic astrology and explore your cosmic journey with our comprehensive tools and insights."}, "dashboard": {"title": "Dashboard", "description": "Overview of your astrological insights and analytics", "revenue": "Revenue Analytics", "revenueDescription": "Track your financial growth and revenue trends over time", "performance": "Performance Metrics", "performanceDescription": "Monitor key performance indicators and system metrics", "users": "User Analytics", "usersDescription": "Analyze user engagement and demographic insights", "activity": "Activity Overview", "activityDescription": "Recent activities and system events summary"}, "about": {"title": "About Nakshatra Cafe", "description": "Learn more about our mission to bring Vedic astrology wisdom to the modern world.", "mission": "Our Mission", "missionDescription": "To make ancient Vedic astrology accessible and meaningful for everyone in the digital age.", "vision": "Our Vision", "visionDescription": "Creating a bridge between traditional wisdom and modern technology for spiritual growth.", "team": "Our Team", "teamDescription": "Passionate astrologers and developers working together to serve the community."}, "profile": {"title": "User Profile", "description": "Manage your personal information and astrological preferences."}, "user": {"profile": "Profile", "name": "Name", "email": "Email", "avatar": "Avatar", "preferences": "Preferences", "updateProfile": "Update Profile", "changePassword": "Change Password", "accountSettings": "Account <PERSON><PERSON>"}, "settings": {"title": "Settings", "description": "Customize your experience and manage your preferences.", "general": "General", "generalDescription": "Configure general application settings and preferences.", "appearance": "Appearance", "language": "Language", "languageDescription": "Choose your preferred language for the application.", "theme": "Theme", "themeDescription": "Customize the appearance and colors of the application.", "notifications": "Notifications", "notificationsDescription": "Manage your notification preferences and alerts.", "privacy": "Privacy", "security": "Security", "lightMode": "Light Mode", "darkMode": "Dark Mode", "systemMode": "System Mode", "comingSoon": "Coming soon...", "enableNotifications": "Enable Notifications", "disableNotifications": "Disable Notifications"}, "language": {"selectLanguage": "Select Language", "changeLanguage": "Change Language", "currentLanguage": "Current Language", "english": "English", "hindi": "Hindi", "marathi": "Marathi"}, "notifications": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "profileUpdated": "Profile updated successfully", "settingsSaved": "Setting<PERSON> saved successfully", "languageChanged": "Language changed to {{language}}", "themeChanged": "Theme changed to {{theme}}", "notificationToggled": "Notifications {{status}}", "enabled": "enabled", "disabled": "disabled"}, "forms": {"validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum {{count}} characters required", "maxLength": "Maximum {{count}} characters allowed", "passwordMismatch": "Passwords do not match"}, "placeholders": {"name": "Enter your name", "email": "Enter your email", "password": "Enter your password", "confirmPassword": "Confirm your password", "search": "Search..."}}, "demo": {"title": "Demo Components", "description": "This section demonstrates various features of the application", "counter": "Counter", "increment": "Increment", "decrement": "Decrement", "reset": "Reset", "currentValue": "Current value: {{value}}", "todoList": "Todo List", "addTodo": "Add <PERSON>", "todoPlaceholder": "Enter a new todo...", "noTodos": "No todos yet. Add one above!", "completeTodo": "Mark as complete", "deleteTodo": "Delete todo", "weatherWidget": "Weather Widget", "temperature": "Temperature", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "windSpeed": "Wind Speed", "condition": "Condition"}, "footer": {"copyright": "Nakshatra Cafe. All rights reserved.", "madeWith": "Made with ❤️ using Vite, React, TypeScript, and Tailwind CSS"}}