{"name": "nakshatracafe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@reduxjs/toolkit": "^2.8.2", "@types/redux-persist": "^4.3.1", "ai": "^5.0.33", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "colorizr": "^3.0.8", "i18next": "^25.4.2", "i18next-browser-languagedetector": "^8.2.0", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-i18next": "^15.7.3", "react-redux": "^9.2.0", "react-router-dom": "^7.8.2", "redux-persist": "^6.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@tailwindcss/vite": "^4.1.12", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.3.0", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.0", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-testing-library": "^7.6.6", "globals": "^16.3.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "shadcn": "^3.2.1", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "vitest": "^3.2.4"}}